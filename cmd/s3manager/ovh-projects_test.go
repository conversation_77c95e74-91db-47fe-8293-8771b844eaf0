package s3manager

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// MockOvhProvider is a simple mock that implements the ExecApi method
type MockOvhProvider struct {
	execApiFunc func(method, path, body string) (any, error)
}

func (m *MockOvhProvider) ExecApi(method, path, body string) (any, error) {
	if m.execApiFunc != nil {
		return m.execApiFunc(method, path, body)
	}
	return nil, fmt.Errorf("mock not configured")
}

// mockRunOvhProjects is a testable version of runOvhProjects that accepts a provider
func mockRunOvhProjects(provider *MockOvhProvider) error {
	result, err := provider.ExecApi("get", "/cloud/project", "")
	if err != nil {
		fmt.Printf("API call failed: %v", err)
		return err
	}

	// Pretty print the result
	resultJSON, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		fmt.Printf("%+v\n", result)
	} else {
		fmt.Printf("%s\n", string(resultJSON))
	}

	return nil
}

func TestOvhProjects(t *testing.T) {
	globalConfig = GlobalConfig{
		OvhEndpoint:          "ovh-eu",
		OvhApplicationKey:    "test-app-key",
		OvhApplicationSecret: "test-app-secret",
		OvhConsumerKey:       "test-consumer-key",
		OvhProjectId:         "test-project-id",
	}

	t.Run("list", func(t *testing.T) {
		// Save original stdout
		originalStdout := os.Stdout

		// Create a new pipe for this test
		r, w, err := os.Pipe()
		assert.NoError(t, err)

		// Redirect stdout to our pipe
		os.Stdout = w

		// Create channel and goroutine to capture output
		output := make(chan string)
		go func() {
			var buf bytes.Buffer
			io.Copy(&buf, r)
			output <- buf.String()
		}()

		// Create a mock provider with expected response
		expectedProjects := []string{"project-1", "project-2", "project-3"}
		mockProvider := &MockOvhProvider{
			execApiFunc: func(method, path, body string) (any, error) {
				return expectedProjects, nil
			},
		}

		// Test the mockRunOvhProjects function
		err = mockRunOvhProjects(mockProvider)
		assert.NoError(t, err)

		// Close writer to signal end of output
		w.Close()

		// Restore stdout before reading output
		os.Stdout = originalStdout

		// Read the captured output
		outputStr := <-output
		r.Close()

		// Verify the JSON output
		expectedJSON := `[
  "project-1",
  "project-2",
  "project-3"
]`
		assert.Contains(t, outputStr, expectedJSON)
	})
}
