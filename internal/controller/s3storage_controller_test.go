package controller

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	s3managerv1 "gitlab.mtk.zone/mt-public/s3manager/api/v1"
)

func TestS3StorageController(t *testing.T) {
	// Create a fake client and context for testing
	scheme := runtime.NewScheme()
	err := s3managerv1.AddToScheme(scheme)
	assert.NoError(t, err)
	k8sClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	ctx := context.Background()

	reconciler := &S3StorageReconciler{
		Client: k8sClient,
		Scheme: k8sClient.Scheme(),
	}
	s3StorageResourceKey := client.ObjectKey{
		Namespace: "default",
		Name:      "test",
	}
	s3StorageResource := &s3managerv1.S3Storage{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      "test",
		},
		Spec: s3managerv1.S3StorageSpec{
			Provider: "ovh",
			Region:   "eu-west-par",
		},
	}

	t.Run("Create S3Storage resource", func(t *testing.T) {
		assert.NoError(t, k8sClient.Create(ctx, s3StorageResource))
		// Verify the created resource
		var resource s3managerv1.S3Storage
		err := k8sClient.Get(ctx, s3StorageResourceKey, &resource)
		assert.NoError(t, err)
		assert.Equal(t, resource.Spec.Provider, "ovh")
		assert.Equal(t, resource.Spec.Region, "eu-west-par")
	})

	t.Run("Reconcile no resources", func(t *testing.T) {
		_, err := reconciler.Reconcile(ctx, reconcile.Request{
			NamespacedName: client.ObjectKey{
				Name:      "test",
				Namespace: "default",
			},
		})
		assert.NoError(t, err)
	})
}
